2025-08-02 02:34:47 - APIServer - INFO - 网络访问控制服务初始化成功
2025-08-02 02:34:47 - APIServer - INFO - 用户行为监控服务初始化成功
2025-08-02 02:34:47 - APIServer - INFO - CORS已配置为允许局域网访问
2025-08-01 18:34:47 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-08-01 18:34:47 - APIServer - INFO - 客户端连接: Cnh8poiUt4WnAsYiAAAB
2025-08-01 18:34:48 - APIServer - INFO - 客户端连接: b7p5FlN7P5AgZ8B-AAAD
2025-08-01 18:34:50 - APIServer - INFO - 客户端连接: CxrLqPSkocEfXufoAAAF
2025-08-01 18:34:52 - APIServer - INFO - 客户端连接: OQ1Idv1a7B8zk76XAAAH
2025-08-01 18:35:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-08-01 18:35:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 1, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T01:41:56', 'updated_at': '2025-07-31T01:42:00', 'last_scanned': '2025-07-31T01:42:00.682389', 'file_count': 2}]}
2025-08-01 18:35:04 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-08-01 18:35:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-08-01 18:35:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 1, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T01:41:56', 'updated_at': '2025-07-31T01:42:00', 'last_scanned': '2025-07-31T01:42:00.682389', 'file_count': 2}]}
2025-08-01 18:35:04 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-08-01 18:35:05 - APIServer - INFO - 客户端连接: XTk0eVb5iL314Dq8AAAJ
2025-08-01 18:35:05 - APIServer - INFO - 客户端连接: S9yLN8uLwpRjf5BwAAAL
2025-08-01 18:35:06 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 文件ID: 1, 文件信息: {'id': 1, 'folder_id': 1, 'filename': '乡村振兴22.jpg', 'relative_path': '乡村振兴22.jpg', 'file_size': 4610118, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-03-18T10:22:47.411272', 'created_at': '2025-07-31T01:41:56', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\乡村振兴22.jpg', 'exists': False}
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 1
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 文件名: 乡村振兴22.jpg
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'exists']
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\乡村振兴22.jpg
2025-08-01 18:35:06 - APIServer - WARNING - 缩略图请求失败 - 文件路径不存在: C:\321\2\乡村振兴22.jpg
2025-08-01 18:35:06 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 18:35:06 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 2
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 文件ID: 2, 文件信息: {'id': 2, 'folder_id': 1, 'filename': '系列图1.jpg', 'relative_path': '系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T09:54:08', 'created_at': '2025-07-31T01:41:56', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\系列图1.jpg', 'exists': False}
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 1
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-08-01 18:35:06 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'exists']
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-08-01 18:35:06 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\系列图1.jpg
2025-08-01 18:35:06 - APIServer - WARNING - 缩略图请求失败 - 文件路径不存在: C:\321\2\系列图1.jpg
2025-08-01 18:35:06 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 18:35:07 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 文件ID: 1, 文件信息: {'id': 1, 'folder_id': 1, 'filename': '乡村振兴22.jpg', 'relative_path': '乡村振兴22.jpg', 'file_size': 4610118, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-03-18T10:22:47.411272', 'created_at': '2025-07-31T01:41:56', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\乡村振兴22.jpg', 'exists': False}
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 1
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 文件名: 乡村振兴22.jpg
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'exists']
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\乡村振兴22.jpg
2025-08-01 18:35:07 - APIServer - WARNING - 缩略图请求失败 - 文件路径不存在: C:\321\2\乡村振兴22.jpg
2025-08-01 18:35:07 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 18:35:07 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 2
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 文件ID: 2, 文件信息: {'id': 2, 'folder_id': 1, 'filename': '系列图1.jpg', 'relative_path': '系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T09:54:08', 'created_at': '2025-07-31T01:41:56', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\系列图1.jpg', 'exists': False}
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 1
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-08-01 18:35:07 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'exists']
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-08-01 18:35:07 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\系列图1.jpg
2025-08-01 18:35:07 - APIServer - WARNING - 缩略图请求失败 - 文件路径不存在: C:\321\2\系列图1.jpg
2025-08-01 18:35:07 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
